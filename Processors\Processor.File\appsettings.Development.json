{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Warning", "MassTransit": "Debug", "Hazelcast": "Debug", "FileProcessorApplication": "Debug", "Processor.File.FileProcessorApplication": "Debug"}}, "ProcessorConfiguration": {"Version": "1.1.1", "Name": "FileProcessor", "Description": "Enhanced file processor application for development and testing", "InputSchemaId": "65172d5a-57a0-4a5b-a905-a81ccc791e90", "OutputSchemaId": "65172d5a-57a0-4a5b-a905-a81ccc791e90"}, "RabbitMQ": {"Host": "localhost", "VirtualHost": "/", "Username": "guest", "Password": "guest", "RetryLimit": 3, "RetryInterval": "00:00:10", "PrefetchCount": 4, "ConcurrencyLimit": 2}, "OpenTelemetry": {"Endpoint": "http://localhost:4317", "UseConsoleInDevelopment": true, "ServiceName": "EnhancedFileProcessor-Dev", "ServiceVersion": "3.0-20250108-enhanced-dev"}, "Hazelcast": {"ClusterName": "FileProcessorCluster-Dev", "NetworkConfig": {"Addresses": ["127.0.0.1:5701"]}, "ConnectionTimeout": "00:00:10"}, "SchemaValidation": {"EnableInputValidation": false, "EnableOutputValidation": false, "FailOnValidationError": false, "LogValidationWarnings": true, "LogValidationErrors": true, "IncludeValidationTelemetry": true}}