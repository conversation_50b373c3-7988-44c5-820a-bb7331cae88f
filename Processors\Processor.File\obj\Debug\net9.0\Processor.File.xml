<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Processor.File</name>
    </assembly>
    <members>
        <member name="T:Processor.File.FileProcessorApplication">
            <summary>
            Sample concrete implementation of BaseProcessorApplication
            Demonstrates how to create a specific processor service
            The base class now provides a complete default implementation that can be overridden if needed
            </summary>
        </member>
        <member name="M:Processor.File.FileProcessorApplication.ConfigureLogging(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Override to add console logging for debugging
            </summary>
        </member>
        <member name="M:Processor.File.FileProcessorApplication.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Override to add FileProcessor-specific services
            </summary>
        </member>
        <member name="M:Processor.File.FileProcessorApplication.InitializeCustomMetricsServicesAsync">
            <summary>
            Override to initialize file-specific metrics services
            </summary>
        </member>
        <member name="M:Processor.File.FileProcessorApplication.ProcessActivityDataAsync(System.Guid,System.Guid,System.Guid,System.Guid,System.Collections.Generic.List{Shared.Models.AssignmentModel},System.String,System.Guid,System.Threading.CancellationToken)">
            <summary>
            Concrete implementation of the activity processing logic
            This is where the specific processor business logic is implemented
            Handles input parsing and validation internally
            Returns a collection with a single item containing a new ExecutionId
            </summary>
        </member>
        <member name="T:Processor.File.Services.IProcessorFileMetricsService">
            <summary>
            Service for recording processor file metrics.
            </summary>
        </member>
        <member name="M:Processor.File.Services.IProcessorFileMetricsService.RecordDeserializedInputData(System.String,System.String,System.Nullable{System.Guid})">
            <summary>
            Records Deserialized InputData metrics
            </summary>
            <param name="processorName">Processor name for labeling</param>
            <param name="processorVersion">Processor version for labeling</param>
            <param name="orchestratedFlowId">The orchestrated flow ID for filtering (optional)</param>
        </member>
        <member name="T:Processor.File.Services.ProcessorFileMetricsService">
            <summary>
            Service for recording processor file metrics.
            </summary>
        </member>
        <member name="T:Processor.File.ProcessorImplementationHash">
            <summary>
            Auto-generated class containing SHA-256 hash of processor implementation files.
            Used for runtime integrity validation to ensure version consistency.
            </summary>
        </member>
        <member name="P:Processor.File.ProcessorImplementationHash.Hash">
            <summary>
            SHA-256 hash of the processor implementation file: FileProcessorApplication.cs
            </summary>
        </member>
        <member name="F:Processor.File.ProcessorImplementationHash.Version">
            <summary>
            Processor version from assembly information.
            </summary>
        </member>
        <member name="F:Processor.File.ProcessorImplementationHash.Name">
            <summary>
            Processor name from assembly information.
            </summary>
        </member>
        <member name="F:Processor.File.ProcessorImplementationHash.GeneratedAt">
            <summary>
            Timestamp when hash was generated.
            </summary>
        </member>
        <member name="F:Processor.File.ProcessorImplementationHash.SourceFile">
            <summary>
            Source file that was hashed.
            </summary>
        </member>
    </members>
</doc>
