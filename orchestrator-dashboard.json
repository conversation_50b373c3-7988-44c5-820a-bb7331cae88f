{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"id": 1, "title": "Orchestrator Health & Status", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "collapsed": false}, {"id": 5, "title": "Memory Usage", "description": "Maximum memory usage across all orchestrator instances.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "max(orchestrator_memory_usage_bytes_Current_memory_usage_in_bytes)", "instant": false, "legendFormat": "Memory Usage", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}}, {"id": 23, "title": "CPU Usage", "description": "Maximum CPU usage percentage across all orchestrator instances.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "max(orchestrator_cpu_usage_percent_Current_CPU_usage_percentage_0_100)", "instant": false, "legendFormat": "CPU Usage", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}}, {"id": 21, "title": "Health Check Rate", "description": "Rate of health checks performed per second by component and status. Shows how frequently each health check is being executed.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (health_check_name, health_check_status) (rate(orchestrator_health_checks_Total_number_of_health_checks_performed_total[5m]))", "instant": false, "legendFormat": "{{health_check_name}} ({{health_check_status}})", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}, "overrides": [{"matcher": {"id": "byRegexp", "options": ".*Healthy.*"}, "properties": [{"id": "color", "value": {"mode": "fixed", "fixedColor": "green"}}]}, {"matcher": {"id": "byRegexp", "options": ".*Unhealthy.*"}, "properties": [{"id": "color", "value": {"mode": "fixed", "fixedColor": "red"}}]}, {"matcher": {"id": "byRegexp", "options": ".*Degraded.*"}, "properties": [{"id": "color", "value": {"mode": "fixed", "fixedColor": "yellow"}}]}]}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}}, {"id": 30, "title": "Current Health Status", "description": "Worst health status across all orchestrator instances. Values: 0=Healthy (Green), 1=Degraded (Yellow), 2=Unhealthy (Red).", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "max(orchestrator_health_status_Current_health_status_of_the_orchestrator_0_Healthy_1_Degraded_2_Unhealthy)", "instant": true, "legendFormat": "Health Status", "range": false, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 2}]}, "mappings": [{"options": {"0": {"text": "Healthy", "color": "green"}, "1": {"text": "Degraded", "color": "yellow"}, "2": {"text": "Unhealthy", "color": "red"}}, "type": "value"}], "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "value_and_name", "colorMode": "background", "graphMode": "none", "justifyMode": "auto"}}, {"id": 22, "title": "Health Status Over Time", "description": "Orchestrator health status trends over time (0=Healthy, 1=Degraded, 2=Unhealthy). Shows when orchestrators become unhealthy or recover.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "max(orchestrator_health_status_Current_health_status_of_the_orchestrator_0_Healthy_1_Degraded_2_Unhealthy)", "instant": false, "legendFormat": "Health Status", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 2}]}, "mappings": [{"options": {"0": {"text": "Healthy", "color": "green"}, "1": {"text": "Degraded", "color": "yellow"}, "2": {"text": "Unhealthy", "color": "red"}}, "type": "value"}], "custom": {"drawStyle": "line", "lineInterpolation": "stepAfter", "lineWidth": 3, "fillOpacity": 20, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "auto", "pointSize": 6, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "Health Status", "axisColorMode": "text", "scaleDistribution": {"type": "linear"}, "axisCenteredZero": false, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "area"}}, "min": 0, "max": 2}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 9}}, {"id": 25, "title": "Orchestrator Specific Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "collapsed": false}, {"id": 26, "title": "Orchestrator Uptime", "description": "Maximum uptime across all orchestrator instances in seconds. Shows the longest running instance.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "max(orchestrator_uptime_seconds_Total_uptime_of_the_orchestrator_in_seconds)", "instant": true, "legendFormat": "Uptime", "range": false, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 18}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "value_and_name", "colorMode": "value", "graphMode": "none", "justifyMode": "auto"}}, {"id": 27, "title": "Orchestrator Starts", "description": "Total number of starts across all orchestrator instances. Shows combined restart frequency.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_starts_Total_number_of_times_the_orchestrator_has_been_started_total)", "instant": true, "legendFormat": "Starts", "range": false, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 18}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "value_and_name", "colorMode": "value", "graphMode": "none", "justifyMode": "auto"}}, {"id": 28, "title": "Cache Active Entries", "description": "Total active cache entries across all orchestrator instances. Shows combined cache utilization.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_cache_active_entries_total_Total_number_of_active_cache_entries)", "instant": true, "legendFormat": "Active Entries", "range": false, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 18}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "value_and_name", "colorMode": "value", "graphMode": "none", "justifyMode": "auto"}}, {"id": 29, "title": "Cache Average Entry Age", "description": "Average age of cache entries across all orchestrator instances. Shows overall cache freshness.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "avg(orchestrator_cache_average_entry_age_seconds_Average_age_of_cache_entries_in_seconds)", "instant": false, "legendFormat": "Average Entry Age", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 18}}, {"id": 32, "title": "Exceptions", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "collapsed": false}, {"id": 30, "title": "Exception Rate", "description": "Rate of exceptions thrown by the orchestrator per second by type and severity. Shows orchestrator error patterns and stability.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (exception_type, severity) (rate(orchestrator_exceptions_total[5m])) or vector(0)", "instant": false, "legendFormat": "{{exception_type}} ({{severity}})", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}, "overrides": [{"matcher": {"id": "byRegexp", "options": ".*critical.*"}, "properties": [{"id": "color", "value": {"mode": "fixed", "fixedColor": "red"}}]}, {"matcher": {"id": "byRegexp", "options": ".*error.*"}, "properties": [{"id": "color", "value": {"mode": "fixed", "fixedColor": "orange"}}]}, {"matcher": {"id": "byRegexp", "options": ".*warning.*"}, "properties": [{"id": "color", "value": {"mode": "fixed", "fixedColor": "yellow"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}}, {"id": 31, "title": "Total Exceptions", "description": "Total number of exceptions thrown by the orchestrator. Shows overall orchestrator stability.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_exceptions_total) or vector(0)", "instant": true, "legendFormat": "Total Exceptions", "range": false, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 8, "w": 8, "x": 12, "y": 27}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "value_and_name", "colorMode": "value", "graphMode": "none", "justifyMode": "auto"}}, {"id": 33, "title": "Critical Exceptions", "description": "Total number of critical exceptions that affect orchestrator operation. These require immediate attention.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_critical_exceptions_total) or vector(0)", "instant": true, "legendFormat": "Critical Exceptions", "range": false, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "orange", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 4, "x": 20, "y": 27}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "value_and_name", "colorMode": "value", "graphMode": "none", "justifyMode": "auto"}}, {"id": 34, "title": "Commands & Events Processing", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "collapsed": false}, {"id": 35, "title": "Commands Consumed (Rate)", "description": "Rate of activity events consumed by the orchestrator per second (ActivityExecutedEvent and ActivityFailedEvent). Filtered by orchestrated flow ID.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (orchestrated_flow_id) (rate(orchestrator_commands_consumed_Total_number_of_commands_consumed_by_the_orchestrator_total{orchestrated_flow_id=~\"$orchestrated_flow_id\"}[5m]))", "instant": false, "legendFormat": "{{orchestrated_flow_id}} - Commands/sec", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 36}}, {"id": 36, "title": "Events Published (Rate)", "description": "Rate of ExecuteActivityCommand events published by the orchestrator per second. Filtered by orchestrated flow ID.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (orchestrated_flow_id) (rate(orchestrator_events_published_Total_number_of_events_published_by_the_orchestrator_total{orchestrated_flow_id=~\"$orchestrated_flow_id\"}[5m]))", "instant": false, "legendFormat": "{{orchestrated_flow_id}} - Events/sec", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 36}}, {"id": 37, "title": "Processing Success Rate", "description": "Success rate percentage of command processing (successful vs total). Filtered by orchestrated flow ID.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "(sum by (orchestrated_flow_id) (orchestrator_commands_consumed_successful_Total_number_of_commands_consumed_successfully_by_the_orchestrator_total{orchestrated_flow_id=~\"$orchestrated_flow_id\"}) / sum by (orchestrated_flow_id) (orchestrator_commands_consumed_Total_number_of_commands_consumed_by_the_orchestrator_total{orchestrated_flow_id=~\"$orchestrated_flow_id\"})) * 100", "instant": false, "legendFormat": "{{orchestrated_flow_id}} - Success Rate", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "palette-classic"}, "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 36}}, {"id": 38, "title": "Processing Anomaly Detection", "description": "Real-time processing anomaly detection - shows difference between consumed commands and published events. Values > 0 indicate anomalies. Filtered by orchestrated flow ID.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "abs(sum by (orchestrated_flow_id) (orchestrator_commands_consumed_Total_number_of_commands_consumed_by_the_orchestrator_total{orchestrated_flow_id=~\"$orchestrated_flow_id\"}) - sum by (orchestrated_flow_id) (orchestrator_events_published_Total_number_of_events_published_by_the_orchestrator_total{orchestrated_flow_id=~\"$orchestrated_flow_id\"}))", "instant": false, "legendFormat": "{{orchestrated_flow_id}} - Flow Anomaly", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 44}}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["orchestrator", "metrics", "monitoring", "dotnet"], "templating": {"list": [{"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": null, "definition": "label_values(orchestrator_commands_consumed_Total_number_of_commands_consumed_by_the_orchestrator_total, orchestrated_flow_id)", "hide": 0, "includeAll": true, "label": "Orchestrated Flow ID", "multi": true, "name": "orchestrated_flow_id", "options": [], "query": {"query": "label_values(orchestrator_commands_consumed_Total_number_of_commands_consumed_by_the_orchestrator_total, orchestrated_flow_id)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Orchestrator Manager Dashboard - Aggregated View", "uid": "orchestrator-dashboard", "version": 2, "weekStart": ""}