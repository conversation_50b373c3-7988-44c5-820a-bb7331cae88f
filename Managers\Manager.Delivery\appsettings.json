﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "MassTransit": "Information",
      "MongoDB": "Information"
    }
  },
  "AllowedHosts": "*",
  "ManagerConfiguration": {
    "Version": "1.0.0",
    "Name": "DeliveryManager",
    "Description": "Manager for delivery entities and operations"
  },
  "ConnectionStrings": {
    "MongoDB": "mongodb://localhost:27017"
  },
  "MongoDB": {
    "DatabaseName": "ManagerDeliveryDB"
  },
  "RabbitMQ": {
    "Host": "localhost",
    "VirtualHost": "/",
    "Username": "guest",
    "Password": "guest"
  },
  "OpenTelemetry": {
    "Endpoint": "http://localhost:4317",
    "HealthEndpoint": "http://localhost:8081",
    "UseConsoleInDevelopment": false,
    "ServiceName": "DeliveryManager",
    "ServiceVersion": "1.0.0"
  },
  "Features": {
    "ReferentialIntegrityValidation": true
  },
  "ReferentialIntegrity": {
    "ValidationTimeoutMs": 3000,
    "EnableParallelValidation": true,
    "ValidateSourceReferences": true,
    "ValidateDestinationReferences": true,
    "ValidateAssignmentReferences": true,
    "ValidateScheduledFlowReferences": true,
    "ValidateStepReferences": true,
    "ValidateFlowReferences": true,
    "ValidateSchemaReferences": true
  },
  "ManagerUrls": {
    "Assignment": "http://localhost:5010",
    "Schema": "http://localhost:5100"
  }
}
