﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "MassTransit": "Information",
      "MongoDB": "Information"
    }
  },
  "AllowedHosts": "*",
  "ManagerConfiguration": {
    "Version": "1.0.0",
    "Name": "ProcessorManager",
    "Description": "Manager for processor entities and operations"
  },
  "ManagerUrls": {
    "StepManager": "http://localhost:5000",
    "Schema": "http://localhost:5100"
  },
  "ReferenceValidation": {
    "RetryCount": 3,
    "CircuitBreakerThreshold": 5,
    "CircuitBreakerDuration": "00:00:30"
  },
  "ConnectionStrings": {
    "MongoDB": "mongodb://localhost:27017"
  },
  "MongoDB": {
    "DatabaseName": "ManagerProcessorDB"
  },
  "RabbitMQ": {
    "Host": "localhost",
    "VirtualHost": "/",
    "Username": "guest",
    "Password": "guest"
  },
  "OpenTelemetry": {
    "Endpoint": "http://localhost:4317",
    "HealthEndpoint": "http://localhost:8081",
    "UseConsoleInDevelopment": false,
    "ServiceName": "ProcessorManager",
    "ServiceVersion": "1.0.0"
  },
  "Features": {
    "ReferentialIntegrityValidation": true
  },
  "ReferentialIntegrity": {
    "ValidationTimeoutMs": 3000,
    "EnableParallelValidation": true,
    "ValidateSourceReferences": true,
    "ValidateDestinationReferences": true,
    "ValidateAssignmentReferences": true,
    "ValidateScheduledFlowReferences": true,
    "ValidateStepReferences": true,
    "ValidateFlowReferences": true,
    "ValidateSchemaReferences": true
  }
}
